/** @jsx createComponent */
/// <reference path="./index.d.ts" />
import { createComponent } from './index';

render(
  <Graph
    data={[
      { name: 'Hello', x: 100, y: 100, id: 1 },
      { name: '<PERSON>unee', x: 200, y: 200, id: 2 },
      { from: 1, to: 2, name: 'Hello\nQunee', type: 'edge' },
    ]}
  />,
);

function Graph(props) {
  // 处理图形数据
  const { data } = props;
  console.log('Graph data:', data);

  // 返回一个表示图形的对象或者null
  return { type: 'graph', data };
}

function render(element) {
  console.log('Rendering:', element);
  // 这里可以添加你的渲染逻辑
}