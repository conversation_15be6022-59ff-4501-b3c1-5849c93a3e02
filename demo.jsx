/** @jsx createComponent */
/// <reference path="./index.d.ts" />
import { createComponent, render } from './index';
import { Graph } from './Graph';

// 渲染组件到页面
const graphInstance = render(
  <Graph
    data={[
      { name: 'Hello', x: 100, y: 100, id: 1 },
      { name: 'Qunee', x: 200, y: 200, id: 2 },
      { from: 1, to: 2, name: 'Hello→Qunee', type: 'edge' },
    ]}
    width={500}
    height={400}
    onNodeClick={(node) => {
      console.log('Node clicked:', node);
      alert(`Clicked: ${node.name}`);
    }}
  />,
  document.body
);

// 获取组件实例，调用API
const graph = graphInstance.getInstance();

// 示例：3秒后添加新节点
setTimeout(() => {
  graph.addNode({ name: 'New Node', x: 300, y: 150, id: 3 });
  console.log('Added new node');
}, 3000);

// 示例：6秒后更新数据
setTimeout(() => {
  graph.updateData([
    { name: 'A', x: 80, y: 80, id: 1 },
    { name: 'B', x: 220, y: 80, id: 2 },
    { name: 'C', x: 150, y: 200, id: 3 },
    { from: 1, to: 2, name: 'A→B', type: 'edge' },
    { from: 2, to: 3, name: 'B→C', type: 'edge' },
    { from: 3, to: 1, name: 'C→A', type: 'edge' },
  ]);
  console.log('Updated graph data');
}, 6000);

// 示例：10秒后卸载组件
setTimeout(() => {
  const success = graphInstance.unmount();
  console.log('Component unmounted:', success);
}, 10000);