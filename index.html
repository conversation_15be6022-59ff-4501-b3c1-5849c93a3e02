<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas JSX 组件演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #da190b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas JSX 组件演示</h1>
        
        <div class="info">
            <h3>功能演示：</h3>
            <ul>
                <li>✅ JSX语法创建canvas组件</li>
                <li>✅ 组件生命周期管理（mount/unmount）</li>
                <li>✅ 事件处理（点击节点）</li>
                <li>✅ API调用（添加节点、更新数据）</li>
                <li>✅ 自动卸载演示</li>
            </ul>
        </div>

        <div class="controls">
            <button onclick="addRandomNode()">添加随机节点</button>
            <button onclick="clearGraph()">清空图形</button>
            <button onclick="resetGraph()">重置图形</button>
            <button class="danger" onclick="unmountGraph()">卸载组件</button>
        </div>

        <!-- 组件将渲染到这里 -->
        <div id="graph-container"></div>
    </div>

    <script type="module">
        import { createComponent, render } from './index.js';
        import { Graph } from './Graph.js';

        let graphInstance = null;
        let nodeIdCounter = 10;

        // 初始化图形
        function initGraph() {
            if (graphInstance) {
                graphInstance.unmount();
            }

            const container = document.getElementById('graph-container');
            container.innerHTML = ''; // 清空容器

            // 使用JSX语法创建组件
            const element = createComponent(Graph, {
                data: [
                    { name: 'Hello', x: 100, y: 100, id: 1 },
                    { name: 'Qunee', x: 200, y: 200, id: 2 },
                    { from: 1, to: 2, name: 'Hello→Qunee', type: 'edge' },
                ],
                width: 500,
                height: 400,
                onNodeClick: (node) => {
                    alert(`点击了节点: ${node.name} (ID: ${node.id})`);
                }
            });

            graphInstance = render(element, container);
        }

        // 添加随机节点
        window.addRandomNode = function() {
            if (!graphInstance) return;
            
            const graph = graphInstance.getInstance();
            const x = Math.random() * 400 + 50;
            const y = Math.random() * 300 + 50;
            
            graph.addNode({
                name: `Node${nodeIdCounter}`,
                x: x,
                y: y,
                id: nodeIdCounter++
            });
        };

        // 清空图形
        window.clearGraph = function() {
            if (!graphInstance) return;
            
            const graph = graphInstance.getInstance();
            graph.updateData([]);
        };

        // 重置图形
        window.resetGraph = function() {
            initGraph();
        };

        // 卸载组件
        window.unmountGraph = function() {
            if (graphInstance) {
                const success = graphInstance.unmount();
                if (success) {
                    graphInstance = null;
                    alert('组件已卸载');
                }
            }
        };

        // 页面加载时初始化
        initGraph();

        // 演示自动操作
        setTimeout(() => {
            if (graphInstance) {
                const graph = graphInstance.getInstance();
                graph.addNode({ name: 'Auto', x: 300, y: 150, id: 3 });
                console.log('自动添加了新节点');
            }
        }, 2000);
    </script>
</body>
</html>
