/**
 * Class作为jsx节点类型 - 默认构造函数参数为props,
 */
declare const Fragment: typeof Container;
declare function Container(): void;
type Props = Record<string, any>;
type JSXComponent = {
  type: Function;
  props: any;
};
declare namespace JSX {
  type Element = JSXComponent | undefined;
  interface IntrinsicElements {}
  type IntrinsicAttributes = Props & {};
}
declare function createComponent(
  type: Function,
  props: JSX.IntrinsicAttributes,
  ...children: any[]
): JSXComponent;
declare function isJSXElement(e: any): e is JSXComponent;

export { Fragment, JSX, type Props, createComponent, isJSXElement };
