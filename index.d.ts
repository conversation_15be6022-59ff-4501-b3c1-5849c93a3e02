/**
 * Class作为jsx节点类型 - 默认构造函数参数为props,
 */
declare const Fragment: typeof Container;
declare function Container(): void;
type Props = Record<string, any>;
type JSXComponent = {
  type: Function;
  props: any;
};

// 定义JSX组件函数类型 - 允许返回任何值
type ComponentFunction = (props?: any) => any;

declare namespace JSX {
  type Element = JSXComponent | any;
  interface IntrinsicElements {
    [elemName: string]: any;
  }
  type IntrinsicAttributes = Props & {};
  type ElementType = ComponentFunction | string;
}
declare function createComponent(
  type: Function,
  props: JSX.IntrinsicAttributes,
  ...children: any[]
): JSXComponent;
declare function isJSXElement(e: any): e is JSXComponent;

// 组件实例接口
interface ComponentInstance {
  mount?(container: HTMLElement): void;
  unmount?(): void;
  [key: string]: any;
}

// 渲染结果接口
interface RenderResult {
  id: number;
  unmount(): boolean;
  getInstance(): ComponentInstance | null;
}

// 渲染函数
declare function render(element: JSXComponent, container: HTMLElement): RenderResult;
declare function unmount(id: number): boolean;

// JSX运行时函数
declare function jsx(type: any, props: any, key?: any): JSXComponent;
declare function jsxs(type: any, props: any, key?: any): JSXComponent;

export {
  Fragment,
  JSX,
  type Props,
  type ComponentInstance,
  type RenderResult,
  createComponent,
  isJSXElement,
  jsx,
  jsxs,
  render,
  unmount
};
