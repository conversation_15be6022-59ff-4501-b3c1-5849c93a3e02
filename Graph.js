// Graph组件实现
export function Graph(props) {
  const { data = [], width = 400, height = 300 } = props;
  
  return {
    // 组件挂载到容器
    mount(container) {
      // 创建canvas
      this.canvas = document.createElement('canvas');
      this.canvas.width = width;
      this.canvas.height = height;
      this.canvas.style.border = '1px solid #ccc';
      
      this.ctx = this.canvas.getContext('2d');
      container.appendChild(this.canvas);
      
      // 绑定事件
      this.canvas.addEventListener('click', this.handleClick.bind(this));
      this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
      
      // 初始化数据
      this.data = data;
      this.nodes = [];
      this.edges = [];
      
      this.parseData();
      this.render();
    },
    
    // 组件卸载
    unmount() {
      if (this.canvas) {
        this.canvas.removeEventListener('click', this.handleClick);
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        this.canvas.remove();
      }
    },
    
    // 解析数据
    parseData() {
      this.nodes = this.data.filter(item => !item.type || item.type === 'node');
      this.edges = this.data.filter(item => item.type === 'edge');
    },
    
    // 渲染图形
    render() {
      if (!this.ctx) return;
      
      // 清空画布
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      
      // 绘制边
      this.edges.forEach(edge => this.drawEdge(edge));
      
      // 绘制节点
      this.nodes.forEach(node => this.drawNode(node));
    },
    
    // 绘制节点
    drawNode(node) {
      const { x, y, name, id } = node;
      const radius = 20;
      
      // 绘制圆形
      this.ctx.beginPath();
      this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
      this.ctx.fillStyle = '#4CAF50';
      this.ctx.fill();
      this.ctx.strokeStyle = '#333';
      this.ctx.stroke();
      
      // 绘制文字
      this.ctx.fillStyle = '#fff';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(name || id, x, y + 4);
    },
    
    // 绘制边
    drawEdge(edge) {
      const fromNode = this.nodes.find(n => n.id === edge.from);
      const toNode = this.nodes.find(n => n.id === edge.to);
      
      if (!fromNode || !toNode) return;
      
      this.ctx.beginPath();
      this.ctx.moveTo(fromNode.x, fromNode.y);
      this.ctx.lineTo(toNode.x, toNode.y);
      this.ctx.strokeStyle = '#666';
      this.ctx.lineWidth = 2;
      this.ctx.stroke();
      
      // 绘制箭头
      this.drawArrow(fromNode.x, fromNode.y, toNode.x, toNode.y);
      
      // 绘制标签
      if (edge.name) {
        const midX = (fromNode.x + toNode.x) / 2;
        const midY = (fromNode.y + toNode.y) / 2;
        this.ctx.fillStyle = '#333';
        this.ctx.font = '10px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(edge.name, midX, midY - 5);
      }
    },
    
    // 绘制箭头
    drawArrow(fromX, fromY, toX, toY) {
      const angle = Math.atan2(toY - fromY, toX - fromX);
      const arrowLength = 10;
      const arrowAngle = Math.PI / 6;
      
      const endX = toX - 20 * Math.cos(angle);
      const endY = toY - 20 * Math.sin(angle);
      
      this.ctx.beginPath();
      this.ctx.moveTo(endX, endY);
      this.ctx.lineTo(
        endX - arrowLength * Math.cos(angle - arrowAngle),
        endY - arrowLength * Math.sin(angle - arrowAngle)
      );
      this.ctx.moveTo(endX, endY);
      this.ctx.lineTo(
        endX - arrowLength * Math.cos(angle + arrowAngle),
        endY - arrowLength * Math.sin(angle + arrowAngle)
      );
      this.ctx.stroke();
    },
    
    // 点击事件
    handleClick(event) {
      const rect = this.canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      const clickedNode = this.getNodeAt(x, y);
      if (clickedNode) {
        console.log('Clicked node:', clickedNode);
        // 触发自定义事件
        if (props.onNodeClick) {
          props.onNodeClick(clickedNode);
        }
      }
    },
    
    // 鼠标移动事件
    handleMouseMove(event) {
      const rect = this.canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      const hoveredNode = this.getNodeAt(x, y);
      this.canvas.style.cursor = hoveredNode ? 'pointer' : 'default';
    },
    
    // 获取指定位置的节点
    getNodeAt(x, y) {
      return this.nodes.find(node => {
        const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
        return distance <= 20;
      });
    },
    
    // API方法：更新数据
    updateData(newData) {
      this.data = newData;
      this.parseData();
      this.render();
    },
    
    // API方法：添加节点
    addNode(node) {
      this.data.push(node);
      this.parseData();
      this.render();
    },
    
    // API方法：删除节点
    removeNode(nodeId) {
      this.data = this.data.filter(item => item.id !== nodeId);
      this.parseData();
      this.render();
    },
    
    // API方法：获取所有节点
    getNodes() {
      return [...this.nodes];
    },
    
    // API方法：获取所有边
    getEdges() {
      return [...this.edges];
    }
  };
}
