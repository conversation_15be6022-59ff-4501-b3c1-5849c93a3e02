// index.ts
var Fragment = Container;
function Container() {
}
function createComponent(type, props, ...children) {
  if (!(type instanceof Function)) {
    throw `jsx type[${type}] error`;
  }
  const comp = { type, props };
  if (children.length && children[0] !== null) {
    children = children.flat().filter(<PERSON><PERSON>an);
    if (!props) {
      comp.props = { children };
    } else {
      props.children = children;
    }
  }
  return comp;
}
function isJSXElement(e) {
  return !!e.type;
}

// JSX运行时函数
function jsx(type, props, key) {
  return createComponent(type, props);
}

function jsxs(type, props, key) {
  return createComponent(type, props);
}

export {
  Fragment,
  createComponent,
  isJSXElement,
  jsx,
  jsxs
};
