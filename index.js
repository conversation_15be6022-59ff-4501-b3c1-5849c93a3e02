// index.ts
var Fragment = Container;
function Container() {
}
function createComponent(type, props, ...children) {
  if (!(type instanceof Function)) {
    throw `jsx type[${type}] error`;
  }
  const comp = { type, props };
  if (children.length && children[0] !== null) {
    children = children.flat().filter(<PERSON><PERSON>an);
    if (!props) {
      comp.props = { children };
    } else {
      props.children = children;
    }
  }
  return comp;
}
function isJSXElement(e) {
  return !!e.type;
}
export {
  Fragment,
  createComponent,
  isJSXElement
};
