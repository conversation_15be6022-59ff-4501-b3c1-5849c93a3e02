// 组件实例管理
const componentInstances = new Map();
let instanceId = 0;

var Fragment = Container;
function Container() {
}

function createComponent(type, props, ...children) {
  if (!(type instanceof Function)) {
    throw `jsx type[${type}] error`;
  }
  const comp = { type, props };
  if (children.length && children[0] !== null) {
    children = children.flat().filter(Boolean);
    if (!props) {
      comp.props = { children };
    } else {
      props.children = children;
    }
  }
  return comp;
}

function isJSXElement(e) {
  return !!e.type;
}

// 渲染函数 - 返回组件实例用于卸载
function render(element, container) {
  const id = ++instanceId;

  if (isJSXElement(element)) {
    // 执行组件函数获取实例
    const instance = element.type(element.props);

    // 如果组件有mount方法，调用它
    if (instance && typeof instance.mount === 'function') {
      instance.mount(container);
    }

    // 保存实例引用
    componentInstances.set(id, {
      instance,
      container,
      element
    });

    return {
      id,
      unmount: () => unmount(id),
      getInstance: () => instance
    };
  }

  throw new Error('Invalid element type');
}

// 卸载组件
function unmount(id) {
  const componentData = componentInstances.get(id);
  if (!componentData) return false;

  const { instance, container } = componentData;

  // 如果组件有unmount方法，调用它
  if (instance && typeof instance.unmount === 'function') {
    instance.unmount();
  }

  // 清理canvas或DOM
  if (container && container.tagName === 'CANVAS') {
    const ctx = container.getContext('2d');
    ctx.clearRect(0, 0, container.width, container.height);
  }

  componentInstances.delete(id);
  return true;
}

// JSX运行时函数
function jsx(type, props, key) {
  return createComponent(type, props);
}

function jsxs(type, props, key) {
  return createComponent(type, props);
}

export {
  Fragment,
  createComponent,
  isJSXElement,
  jsx,
  jsxs,
  render,
  unmount
};
